// app/Middleware/TenantResolver.ts
import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import db from '@adonisjs/lucid/services/db'

export default class TenantResolver {
  public async handle({ request }: HttpContext, next: NextFn) {
    // 👇 You can choose to resolve by subdomain or header
    const tenantSlug = request.header('x-tenant') 
      || request.subdomains()[0] // e.g., acme.my-ai-studio.com → "acme"

    if (!tenantSlug) {
      throw new Error('Tenant not specified')
    }

    // 🔎 Look up tenant in master DB
    const tenant = await db.connection('master')
      .from('tenants')
      .where('slug', tenantSlug)
      .firstOrFail()

    // ⚡️ Dynamically register tenant DB connection (overwrite per request)
    db.manager.add('tenant', {
      client: 'pg',
      connection: {
        host: tenant.db_host,
        port: tenant.db_port,
        user: tenant.db_user,
        password: tenant.db_password,
        database: tenant.db_name,
      },
    })

    // 🟢 Continue request
    await next()
  }
}
